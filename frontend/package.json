{"name": "saut-al-quran-frontend", "version": "1.0.0", "private": true, "dependencies": {"@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "react-scripts": "5.0.1", "typescript": "^5.0.0", "web-vitals": "^3.3.0", "wavesurfer.js": "^7.0.0", "@types/wavesurfer.js": "^6.0.0"}, "devDependencies": {"@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/user-event": "^14.4.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}