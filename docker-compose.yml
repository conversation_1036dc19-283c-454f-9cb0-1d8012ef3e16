version: '3.9'

services:
    frontend:
        build: ./frontend
        ports:
            - '3000:3000'
        environment:
            - REACT_APP_API_URL=http://localhost:8000
        depends_on:
            - backend
        volumes:
            - ./frontend:/app
            - /app/node_modules

    backend:
        build: ./backend
        ports:
            - '8000:8000'
        env_file:
            - .env
        depends_on:
            - db
        volumes:
            - ./backend:/app

    db:
        image: postgres:15
        environment:
            - POSTGRES_USER=${POSTGRES_USER}
            - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
            - POSTGRES_DB=${POSTGRES_DB}
        ports:
            - '5432:5432'
        volumes:
            - postgres_data:/var/lib/postgresql/data

    nginx:
        image: nginx:latest
        volumes:
            - ./nginx/nginx.conf:/etc/nginx/nginx.conf
        ports:
            - '80:80'
        depends_on:
            - frontend
            - backend

volumes:
    postgres_data:
