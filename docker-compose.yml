version: '3.9'

services:
    frontend:
        build: ./frontend
        ports:
            - '3000:3000'
        depends_on:
            - backend

    backend:
        build: ./backend
        ports:
            - '8000:8000'
        env_file:
            - .env
        volumes:
            - ./backend/app:/app

    nginx:
        image: nginx:latest
        volumes:
            - ./nginx/nginx.conf:/etc/nginx/nginx.conf
        ports:
            - '80:80'
        depends_on:
            - frontend
            - backend
